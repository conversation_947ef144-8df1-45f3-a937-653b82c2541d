import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import org.apache.spark.sql.SparkSession

/**
 * 咖啡连锁店数据分析
 * 包括数据预处理、销售排名分析、多维度销售分布分析
 */
object CoffeeChainAnalysis {
  
  // 定义咖啡数据的case class
  case class CoffeeData(
    areaCode: String,
    date: String,
    market: String,
    marketSize: String,
    product: String,
    productType: String,
    state: String,
    coffeeType: String,
    budgetCogs: Double,
    budgetMargin: Double,
    budgetProfit: Double,
    budgetSales: Double,
    coffeeSales: Double,
    cogs: Double,
    inventory: Double,
    margin: Double,
    marketing: Double,
    numberOfRecords: Int,
    profit: Double,
    totalExpenses: Double
  )
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Coffee Chain Analysis")
      .setMaster("local[*]")
    
    val sc = new SparkContext(conf)
    
    try {
      println("=== 咖啡连锁店数据分析 ===")
      
      // 1. 数据预处理
      println("\n1. 数据预处理...")
      val inputFile = "/home/<USER>/me/CoffeeChain.csv"
      
      // 读取CSV文件
      val rawData = sc.textFile(inputFile)
      
      // 获取表头
      val header = rawData.first()
      println(s"数据表头：$header")
      
      // 过滤掉表头，获取数据行
      val dataLines = rawData.filter(_ != header)
      val totalRecords = dataLines.count()
      println(s"总数据记录数：$totalRecords")
      
      // 解析数据
      val coffeeDataRDD = dataLines.map(parseCSVLine).filter(_.isDefined).map(_.get)
      
      // 缓存数据以提高性能
      coffeeDataRDD.cache()
      
      val validRecords = coffeeDataRDD.count()
      println(s"有效数据记录数：$validRecords")
      println(s"数据清洗完成，过滤掉 ${totalRecords - validRecords} 条无效记录")
      
      // 显示数据示例
      println("\n数据示例（前3条）：")
      coffeeDataRDD.take(3).foreach(println)
      
      // 2. 销售量排名分析
      println("\n2. 销售量排名分析...")
      salesRankingAnalysis(coffeeDataRDD, sc)
      
      // 3. 销售分布分析
      println("\n3. 销售分布分析...")
      salesDistributionAnalysis(coffeeDataRDD, sc)
      
      println("\n=== 分析完成 ===")
      
    } catch {
      case e: Exception =>
        println(s"程序执行出错：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 解析CSV行数据
   */
  def parseCSVLine(line: String): Option[CoffeeData] = {
    try {
      val parts = line.split(",")
      if (parts.length >= 20) {
        Some(CoffeeData(
          areaCode = parts(0),
          date = parts(1),
          market = parts(2),
          marketSize = parts(3),
          product = parts(4),
          productType = parts(5),
          state = parts(6),
          coffeeType = parts(7),
          budgetCogs = parts(8).toDouble,
          budgetMargin = parts(9).toDouble,
          budgetProfit = parts(10).toDouble,
          budgetSales = parts(11).toDouble,
          coffeeSales = cleanNumericString(parts(12)).toDouble,
          cogs = parts(13).toDouble,
          inventory = cleanNumericString(parts(14)).toDouble,
          margin = parts(15).toDouble,
          marketing = parts(16).toDouble,
          numberOfRecords = parts(17).toInt,
          profit = parts(18).toDouble,
          totalExpenses = parts(19).toDouble
        ))
      } else {
        None
      }
    } catch {
      case _: Exception => None
    }
  }
  
  /**
   * 清理数字字符串（去除逗号等）
   */
  def cleanNumericString(str: String): String = {
    str.replaceAll("\"", "").replaceAll(",", "")
  }
  
  /**
   * 销售量排名分析
   */
  def salesRankingAnalysis(coffeeDataRDD: org.apache.spark.rdd.RDD[CoffeeData], sc: SparkContext): Unit = {
    println("正在进行销售量排名分析...")
    
    // 按产品统计销售量
    val productSales = coffeeDataRDD
      .map(data => (data.product, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
    
    println("\n产品销售量排名（Top 10）：")
    val topProducts = productSales.take(10)
    topProducts.zipWithIndex.foreach { case ((product, sales), index) =>
      println(s"${index + 1}. $product: ${sales.formatted("%.0f")}")
    }
    
    // 按州统计销售量
    val stateSales = coffeeDataRDD
      .map(data => (data.state, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
    
    println("\n各州销售量排名：")
    val topStates = stateSales.collect()
    topStates.zipWithIndex.foreach { case ((state, sales), index) =>
      println(s"${index + 1}. $state: ${sales.formatted("%.0f")}")
    }
    
    // 保存销售排名结果
    val rankingResults = sc.parallelize(Seq(
      "=== 咖啡销售量排名分析结果 ===",
      "",
      "产品销售量排名（Top 10）：",
      topProducts.zipWithIndex.map { case ((product, sales), index) =>
        s"${index + 1}. $product: ${sales.formatted("%.0f")}"
      }.mkString("\n"),
      "",
      "各州销售量排名：",
      topStates.zipWithIndex.map { case ((state, sales), index) =>
        s"${index + 1}. $state: ${sales.formatted("%.0f")}"
      }.mkString("\n")
    ))
    
    rankingResults.coalesce(1).saveAsTextFile("/home/<USER>/me/coffee_sales_ranking")
    println("\n销售排名结果已保存到：/home/<USER>/me/coffee_sales_ranking")
  }

  /**
   * 销售分布分析
   */
  def salesDistributionAnalysis(coffeeDataRDD: org.apache.spark.rdd.RDD[CoffeeData], sc: SparkContext): Unit = {
    println("正在进行销售分布分析...")

    // 1. 咖啡销售量和state的关系
    println("\n1. 分析咖啡销售量和州的关系...")
    val stateSalesStats = coffeeDataRDD
      .map(data => (data.state, data.coffeeSales))
      .groupByKey()
      .map { case (state, sales) =>
        val salesList = sales.toList
        val total = salesList.sum
        val avg = total / salesList.length
        val max = salesList.max
        val min = salesList.min
        (state, total, avg, max, min, salesList.length)
      }
      .collect()
      .sortBy(-_._2) // 按总销售量降序排列

    println("各州销售统计：")
    println("州名\t\t总销售量\t平均销售量\t最大值\t\t最小值\t\t记录数")
    stateSalesStats.foreach { case (state, total, avg, max, min, count) =>
      println(f"$state%-12s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t${max.formatted("%.0f")}%-10s\t${min.formatted("%.0f")}%-10s\t$count")
    }

    // 2. 咖啡销售量和market的关系
    println("\n2. 分析咖啡销售量和市场的关系...")
    val marketSalesStats = coffeeDataRDD
      .map(data => (data.market, data.coffeeSales))
      .groupByKey()
      .map { case (market, sales) =>
        val salesList = sales.toList
        val total = salesList.sum
        val avg = total / salesList.length
        (market, total, avg, salesList.length)
      }
      .collect()
      .sortBy(-_._2)

    println("各市场销售统计：")
    println("市场\t\t总销售量\t平均销售量\t记录数")
    marketSalesStats.foreach { case (market, total, avg, count) =>
      println(f"$market%-15s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count")
    }

    // 3. 咖啡的平均利润和售价
    println("\n3. 分析咖啡的平均利润和售价...")
    val profitPriceStats = coffeeDataRDD
      .map(data => (data.profit, data.coffeeSales, data.margin))
      .collect()

    val avgProfit = profitPriceStats.map(_._1).sum / profitPriceStats.length
    val avgSales = profitPriceStats.map(_._2).sum / profitPriceStats.length
    val avgMargin = profitPriceStats.map(_._3).sum / profitPriceStats.length

    println(f"平均利润：${avgProfit.formatted("%.2f")}")
    println(f"平均销售额：${avgSales.formatted("%.2f")}")
    println(f"平均利润率：${avgMargin.formatted("%.2f")}")

    // 4. 咖啡的平均利润、售价和销售量的关系
    println("\n4. 分析利润、售价和销售量的关系...")
    val productProfitSales = coffeeDataRDD
      .map(data => (data.product, (data.profit, data.coffeeSales, data.margin)))
      .groupByKey()
      .map { case (product, values) =>
        val valuesList = values.toList
        val avgProfit = valuesList.map(_._1).sum / valuesList.length
        val avgSales = valuesList.map(_._2).sum / valuesList.length
        val avgMargin = valuesList.map(_._3).sum / valuesList.length
        (product, avgProfit, avgSales, avgMargin)
      }
      .collect()
      .sortBy(-_._3) // 按平均销售量排序

    println("各产品利润和销售关系：")
    println("产品\t\t\t平均利润\t平均销售量\t平均利润率")
    productProfitSales.take(10).foreach { case (product, profit, sales, margin) =>
      println(f"$product%-20s\t${profit.formatted("%.2f")}%-10s\t${sales.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}")
    }

    // 5. 咖啡的平均利润、销售量与其他成本的关系
    println("\n5. 分析利润、销售量与成本的关系...")
    val costAnalysis = coffeeDataRDD
      .map(data => (data.profit, data.coffeeSales, data.cogs, data.marketing, data.totalExpenses))
      .collect()

    val avgCogs = costAnalysis.map(_._3).sum / costAnalysis.length
    val avgMarketing = costAnalysis.map(_._4).sum / costAnalysis.length
    val avgTotalExpenses = costAnalysis.map(_._5).sum / costAnalysis.length

    println(f"平均商品成本(COGS)：${avgCogs.formatted("%.2f")}")
    println(f"平均营销费用：${avgMarketing.formatted("%.2f")}")
    println(f"平均总费用：${avgTotalExpenses.formatted("%.2f")}")

    // 计算成本效率
    val profitCostRatio = avgProfit / avgTotalExpenses
    val salesCostRatio = avgSales / avgTotalExpenses
    println(f"利润成本比：${profitCostRatio.formatted("%.3f")}")
    println(f"销售成本比：${salesCostRatio.formatted("%.3f")}")

    // 6. 咖啡属性与平均售价、平均利润、销售量与其他成本的关系
    println("\n6. 分析咖啡属性与各指标的关系...")

    // 按咖啡类型分析
    val typeAnalysis = coffeeDataRDD
      .map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.margin, data.cogs, data.marketing)))
      .groupByKey()
      .map { case (coffeeType, values) =>
        val valuesList = values.toList
        val avgSales = valuesList.map(_._1).sum / valuesList.length
        val avgProfit = valuesList.map(_._2).sum / valuesList.length
        val avgMargin = valuesList.map(_._3).sum / valuesList.length
        val avgCogs = valuesList.map(_._4).sum / valuesList.length
        val avgMarketing = valuesList.map(_._5).sum / valuesList.length
        (coffeeType, avgSales, avgProfit, avgMargin, avgCogs, avgMarketing, valuesList.length)
      }
      .collect()
      .sortBy(-_._2)

    println("按咖啡类型分析：")
    println("类型\t\t平均销售量\t平均利润\t平均利润率\t平均成本\t平均营销\t记录数")
    typeAnalysis.foreach { case (coffeeType, sales, profit, margin, cogs, marketing, count) =>
      println(f"$coffeeType%-10s\t${sales.formatted("%.2f")}%-10s\t${profit.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}%-10s\t${cogs.formatted("%.2f")}%-10s\t${marketing.formatted("%.2f")}%-10s\t$count")
    }

    // 7. 市场规模、市场地域与销售量的关系
    println("\n7. 分析市场规模、地域与销售量的关系...")

    val marketSizeAnalysis = coffeeDataRDD
      .map(data => (data.marketSize, data.coffeeSales))
      .groupByKey()
      .map { case (marketSize, sales) =>
        val salesList = sales.toList
        val total = salesList.sum
        val avg = total / salesList.length
        (marketSize, total, avg, salesList.length)
      }
      .collect()
      .sortBy(-_._2)

    println("按市场规模分析：")
    println("市场规模\t\t总销售量\t平均销售量\t记录数")
    marketSizeAnalysis.foreach { case (marketSize, total, avg, count) =>
      println(f"$marketSize%-15s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count")
    }

    // 保存分析结果
    saveDistributionResults(stateSalesStats, marketSalesStats, productProfitSales,
                           typeAnalysis, marketSizeAnalysis, avgProfit, avgSales,
                           avgMargin, avgCogs, avgMarketing, avgTotalExpenses, sc)
  }

  /**
   * 保存销售分布分析结果
   */
  def saveDistributionResults(stateSalesStats: Array[(String, Double, Double, Double, Double, Int)],
                             marketSalesStats: Array[(String, Double, Double, Int)],
                             productProfitSales: Array[(String, Double, Double, Double)],
                             typeAnalysis: Array[(String, Double, Double, Double, Double, Double, Int)],
                             marketSizeAnalysis: Array[(String, Double, Double, Int)],
                             avgProfit: Double, avgSales: Double, avgMargin: Double,
                             avgCogs: Double, avgMarketing: Double, avgTotalExpenses: Double,
                             sc: SparkContext): Unit = {

    val results = sc.parallelize(Seq(
      "=== 咖啡连锁店销售分布分析结果 ===",
      "",
      "1. 各州销售统计：",
      "州名\t\t总销售量\t平均销售量\t最大值\t\t最小值\t\t记录数",
      stateSalesStats.map { case (state, total, avg, max, min, count) =>
        f"$state%-12s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t${max.formatted("%.0f")}%-10s\t${min.formatted("%.0f")}%-10s\t$count"
      }.mkString("\n"),
      "",
      "2. 各市场销售统计：",
      "市场\t\t总销售量\t平均销售量\t记录数",
      marketSalesStats.map { case (market, total, avg, count) =>
        f"$market%-15s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count"
      }.mkString("\n"),
      "",
      "3. 整体利润和售价统计：",
      f"平均利润：${avgProfit.formatted("%.2f")}",
      f"平均销售额：${avgSales.formatted("%.2f")}",
      f"平均利润率：${avgMargin.formatted("%.2f")}",
      f"平均商品成本(COGS)：${avgCogs.formatted("%.2f")}",
      f"平均营销费用：${avgMarketing.formatted("%.2f")}",
      f"平均总费用：${avgTotalExpenses.formatted("%.2f")}",
      f"利润成本比：${(avgProfit / avgTotalExpenses).formatted("%.3f")}",
      f"销售成本比：${(avgSales / avgTotalExpenses).formatted("%.3f")}",
      "",
      "4. 各产品利润和销售关系（Top 10）：",
      "产品\t\t\t平均利润\t平均销售量\t平均利润率",
      productProfitSales.take(10).map { case (product, profit, sales, margin) =>
        f"$product%-20s\t${profit.formatted("%.2f")}%-10s\t${sales.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}"
      }.mkString("\n"),
      "",
      "5. 按咖啡类型分析：",
      "类型\t\t平均销售量\t平均利润\t平均利润率\t平均成本\t平均营销\t记录数",
      typeAnalysis.map { case (coffeeType, sales, profit, margin, cogs, marketing, count) =>
        f"$coffeeType%-10s\t${sales.formatted("%.2f")}%-10s\t${profit.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}%-10s\t${cogs.formatted("%.2f")}%-10s\t${marketing.formatted("%.2f")}%-10s\t$count"
      }.mkString("\n"),
      "",
      "6. 按市场规模分析：",
      "市场规模\t\t总销售量\t平均销售量\t记录数",
      marketSizeAnalysis.map { case (marketSize, total, avg, count) =>
        f"$marketSize%-15s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count"
      }.mkString("\n")
    ))

    results.coalesce(1).saveAsTextFile("/home/<USER>/me/coffee_distribution_analysis")
    println("\n销售分布分析结果已保存到：/home/<USER>/me/coffee_distribution_analysis")
  }
}
