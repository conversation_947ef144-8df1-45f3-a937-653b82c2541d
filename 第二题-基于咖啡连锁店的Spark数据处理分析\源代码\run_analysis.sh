#!/bin/bash

# 咖啡连锁店Spark数据处理分析运行脚本
# 作者：实训学员
# 日期：$(date)

echo "=== 咖啡连锁店Spark数据处理分析 ==="
echo "开始时间：$(date)"
echo ""

# 检查Spark环境
echo "1. 检查Spark环境..."
if command -v spark-submit &> /dev/null; then
    echo "✓ Spark环境已安装"
    spark-submit --version | head -1
else
    echo "✗ 未找到Spark环境，请先安装Spark"
    exit 1
fi

echo ""

# 检查Scala环境
echo "2. 检查Scala环境..."
if command -v scala &> /dev/null; then
    echo "✓ Scala环境已安装"
    scala -version
else
    echo "✗ 未找到Scala环境，请先安装Scala"
    exit 1
fi

echo ""

# 检查数据文件
echo "3. 检查数据文件..."
if [ -f "/home/<USER>/me/CoffeeChain.csv" ]; then
    echo "✓ 找到咖啡数据文件：/home/<USER>/me/CoffeeChain.csv"
    echo "   文件大小：$(du -h /home/<USER>/me/CoffeeChain.csv | cut -f1)"
    echo "   记录数：$(wc -l < /home/<USER>/me/CoffeeChain.csv)"
else
    echo "✗ 未找到咖啡数据文件：/home/<USER>/me/CoffeeChain.csv"
    exit 1
fi

echo ""

# 第一部分：生成人口年龄数据
echo "=== 第一部分：RDD编程统计人口平均年龄 ==="
echo ""

echo "4. 生成模拟人口年龄数据..."
if [ -f "GeneratePeopleAge.scala" ]; then
    echo "正在编译并运行数据生成程序..."
    scala GeneratePeopleAge.scala
    
    if [ -f "/home/<USER>/me/peopleage.txt" ]; then
        echo "✓ 人口年龄数据生成成功"
        echo "   文件：/home/<USER>/me/peopleage.txt"
        echo "   记录数：$(wc -l < /home/<USER>/me/peopleage.txt)"
    else
        echo "✗ 人口年龄数据生成失败"
        exit 1
    fi
else
    echo "✗ 未找到数据生成程序：GeneratePeopleAge.scala"
    exit 1
fi

echo ""

echo "5. 计算人口平均年龄..."
if [ -f "CalculateAverageAge.scala" ]; then
    echo "正在使用Spark计算平均年龄..."
    spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.scala
    
    if [ -d "/home/<USER>/me/average_age_result.txt" ]; then
        echo "✓ 平均年龄计算完成"
        echo "结果文件：/home/<USER>/me/average_age_result.txt/"
    else
        echo "⚠ 平均年龄计算可能未完全成功，请检查输出"
    fi
else
    echo "✗ 未找到平均年龄计算程序：CalculateAverageAge.scala"
    exit 1
fi

echo ""

# 第二部分：咖啡连锁店数据分析
echo "=== 第二部分：咖啡连锁店数据分析 ==="
echo ""

echo "6. 执行咖啡连锁店数据分析..."
if [ -f "CoffeeChainAnalysis.scala" ]; then
    echo "正在使用Spark进行咖啡数据分析..."
    spark-submit --class CoffeeChainAnalysis --master local[*] CoffeeChainAnalysis.scala
    
    echo "检查分析结果..."
    if [ -d "/home/<USER>/me/coffee_sales_ranking" ]; then
        echo "✓ 销售排名分析完成：/home/<USER>/me/coffee_sales_ranking/"
    fi

    if [ -d "/home/<USER>/me/coffee_distribution_analysis" ]; then
        echo "✓ 销售分布分析完成：/home/<USER>/me/coffee_distribution_analysis/"
    fi
else
    echo "✗ 未找到咖啡分析程序：CoffeeChainAnalysis.scala"
    exit 1
fi

echo ""

# 显示结果摘要
echo "=== 分析结果摘要 ==="
echo ""

echo "生成的文件："
ls -la *.txt 2>/dev/null || echo "  无.txt文件"
echo ""

echo "生成的目录："
ls -la */ 2>/dev/null || echo "  无结果目录"
echo ""

echo "如果要查看详细结果，请运行："
echo "  cat /home/<USER>/me/peopleage.txt | head -10                    # 查看人口数据示例"
echo "  cat /home/<USER>/me/people_age_analysis/part-00000           # 查看平均年龄结果"
echo "  cat /home/<USER>/me/coffee_sales_ranking/part-00000             # 查看销售排名"
echo "  cat /home/<USER>/me/coffee_distribution_analysis/part-00000     # 查看分布分析"

echo ""
echo "分析完成时间：$(date)"
echo "=== 所有分析任务完成 ==="
