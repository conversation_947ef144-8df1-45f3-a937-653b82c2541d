# Spark数据处理实训项目

## 项目概述

本项目是基于Apache Spark的大数据处理实训，包含两个主要任务：
1. RDD编程统计人口平均年龄
2. 咖啡连锁店数据分析

**工作目录：** `/home/<USER>/me/`

## 快速开始

### 方法一：使用快速运行脚本（推荐）

```bash
cd /home/<USER>/me
chmod +x 快速运行.sh
./快速运行.sh
```

### 方法二：使用spark-shell（最简单）

```bash
cd /home/<USER>/me
spark-shell
```

然后在spark-shell中复制粘贴 `spark_commands.scala` 文件中的代码。

## 文件结构

```
/home/<USER>/me/
├── CoffeeChain.csv              # 咖啡数据文件（需要您提供）
├── GeneratePeopleAge.scala      # 生成人口数据程序
├── CalculateAverageAge.scala    # 计算平均年龄程序
├── CoffeeChainAnalysis.scala    # 咖啡数据分析程序
├── spark_commands.scala         # spark-shell版本（推荐）
├── run_analysis.sh              # 完整运行脚本
├── 快速运行.sh                   # 快速运行脚本
├── 实训报告.md                   # 完整实训报告
├── 运行指南.md                   # 详细运行说明
├── 文件清单.md                   # 文件说明
└── README.md                    # 本文件
```

## 运行结果

程序运行后会在 `/home/<USER>/me/` 目录下生成以下结果：

### 数据文件
- `peopleage.txt` - 生成的人口年龄数据

### 分析结果目录
- `people_age_analysis/` - 人口年龄分析结果
- `coffee_analysis_results/` - 咖啡数据分析结果
- `average_age_result.txt/` - 平均年龄计算结果
- `coffee_sales_ranking/` - 咖啡销售排名
- `coffee_distribution_analysis/` - 咖啡销售分布分析

## 查看结果

```bash
# 查看人口数据示例
head -10 /home/<USER>/me/peopleage.txt

# 查看人口年龄分析结果
cat /home/<USER>/me/people_age_analysis/part-00000

# 查看咖啡分析结果
cat /home/<USER>/me/coffee_analysis_results/part-00000
```

## 环境要求

- Java 8 或 Java 11
- Scala 2.12+
- Apache Spark 3.0+
- CentOS虚拟机（图形界面）

## 重要提醒

1. **确保数据文件存在**：请将 `CoffeeChain.csv` 文件放在 `/home/<USER>/me/` 目录下
2. **检查权限**：确保对 `/home/<USER>/me/` 目录有读写权限
3. **内存设置**：如果遇到内存不足，可以调整Spark参数：
   ```bash
   spark-shell --driver-memory 2g --executor-memory 2g
   ```

## 故障排除

### 常见问题

1. **找不到文件**：确保所有文件都在 `/home/<USER>/me/` 目录下
2. **权限问题**：运行 `chmod 755 /home/<USER>/me` 和 `chmod +x *.sh`
3. **内存不足**：增加Spark内存参数
4. **环境问题**：检查Java、Scala、Spark是否正确安装

### 获取帮助

详细的运行说明请参考：
- `运行指南.md` - 完整的运行指导
- `文件清单.md` - 所有文件的详细说明

## 实训报告

完整的实训报告已经在 `实训报告.md` 文件中准备好，您只需要：

1. 填写个人信息（姓名、学号）
2. 运行程序并截图
3. 将实际运行结果替换报告中的示例结果

## 联系信息

如果在运行过程中遇到问题，请检查：
1. 环境是否正确安装
2. 文件路径是否正确
3. 权限是否足够
4. 内存是否充足

祝您实训顺利！🎉
