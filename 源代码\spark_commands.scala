// 在spark-shell中运行的Scala命令
// 使用方法：启动spark-shell，然后复制粘贴以下代码段

println("=== 开始Spark数据分析 ===")

// ===== 第一部分：人口平均年龄计算 =====
println("\n=== 第一部分：人口平均年龄计算 ===")

// 首先生成测试数据（如果还没有的话）
import scala.util.Random
import java.io.PrintWriter
import java.io.File

def generatePeopleData(): Unit = {
  val numRecords = 1000
  val minAge = 18
  val maxAge = 90
  val outputFile = "/home/<USER>/me/peopleage.txt"
  
  println(s"生成 $numRecords 条人口年龄数据...")
  
  val random = new Random()
  val writer = new PrintWriter(new File(outputFile))
  
  try {
    for (i <- 1 to numRecords) {
      val age = minAge + random.nextInt(maxAge - minAge + 1)
      writer.println(s"$i\t$age")
    }
    println(s"数据生成完成：$outputFile")
  } finally {
    writer.close()
  }
}

// 生成数据
generatePeopleData()

// 读取并分析人口数据
val peopleData = sc.textFile("/home/<USER>/me/peopleage.txt")
println(s"读取到 ${peopleData.count()} 条人口记录")

// 显示前5条数据
println("数据示例：")
peopleData.take(5).foreach(println)

// 提取年龄并计算平均值
val ages = peopleData.map(line => line.split("\t")(1).toInt)
ages.cache()

val totalAge = ages.reduce(_ + _)
val count = ages.count()
val averageAge = totalAge.toDouble / count

println(f"\n=== 人口年龄统计结果 ===")
println(f"总人数：$count")
println(f"年龄总和：$totalAge")
println(f"平均年龄：$averageAge%.2f 岁")
println(f"最大年龄：${ages.max()} 岁")
println(f"最小年龄：${ages.min()} 岁")

// 年龄分布统计
val ageGroups = ages.map { age =>
  age match {
    case a if a < 30 => "18-29岁"
    case a if a < 40 => "30-39岁"
    case a if a < 50 => "40-49岁"
    case a if a < 60 => "50-59岁"
    case a if a < 70 => "60-69岁"
    case _ => "70岁以上"
  }
}

val ageGroupCounts = ageGroups.countByValue()
println("\n年龄分布：")
ageGroupCounts.toSeq.sortBy(_._1).foreach { case (group, count) =>
  val percentage = (count.toDouble / peopleData.count() * 100)
  println(f"$group: $count 人 ($percentage%.1f%%)")
}

// 保存结果
val ageResults = sc.parallelize(Seq(
  "=== 人口年龄统计结果 ===",
  s"总人数：$count",
  s"平均年龄：${averageAge.formatted("%.2f")} 岁",
  s"最大年龄：${ages.max()} 岁",
  s"最小年龄：${ages.min()} 岁",
  "年龄分布：",
  ageGroupCounts.toSeq.sortBy(_._1).map { case (group, count) =>
    val percentage = (count.toDouble / peopleData.count() * 100)
    f"$group: $count 人 ($percentage%.1f%%)"
  }.mkString("\n")
))

ageResults.coalesce(1).saveAsTextFile("/home/<USER>/me/people_age_analysis")
println("结果已保存到：/home/<USER>/me/people_age_analysis/")

println("\n=== 第一部分完成 ===")

// ===== 第二部分：咖啡连锁店数据分析 =====
println("\n=== 第二部分：咖啡连锁店数据分析 ===")

// 读取咖啡数据
val coffeeRawData = sc.textFile("/home/<USER>/me/CoffeeChain.csv")
val header = coffeeRawData.first()
val coffeeDataLines = coffeeRawData.filter(_ != header)

println(s"咖啡数据总记录数：${coffeeDataLines.count()}")
println("数据表头：")
println(header)

// 解析数据的简化版本
case class SimpleCoffeeData(
  state: String,
  market: String,
  product: String,
  productType: String,
  coffeeType: String,
  coffeeSales: Double,
  profit: Double,
  margin: Double,
  cogs: Double,
  marketing: Double
)

def parseSimpleCoffeeLine(line: String): Option[SimpleCoffeeData] = {
  try {
    val parts = line.split(",")
    if (parts.length >= 20) {
      Some(SimpleCoffeeData(
        state = parts(6),
        market = parts(2),
        product = parts(4),
        productType = parts(5),
        coffeeType = parts(7),
        coffeeSales = parts(12).replaceAll("\"", "").replaceAll(",", "").toDouble,
        profit = parts(18).toDouble,
        margin = parts(15).toDouble,
        cogs = parts(13).toDouble,
        marketing = parts(16).toDouble
      ))
    } else None
  } catch {
    case _: Exception => None
  }
}

val coffeeData = coffeeDataLines.map(parseSimpleCoffeeLine).filter(_.isDefined).map(_.get)
coffeeData.cache()

val validCoffeeRecords = coffeeData.count()
println(s"有效咖啡数据记录：$validCoffeeRecords")

// 1. 产品销售排名
println("\n1. 产品销售排名分析...")
val productSales = coffeeData
  .map(data => (data.product, data.coffeeSales))
  .reduceByKey(_ + _)
  .sortBy(_._2, ascending = false)

println("产品销售量排名（Top 10）：")
productSales.take(10).zipWithIndex.foreach { case ((product, sales), index) =>
  println(f"${index + 1}. $product: ${sales.formatted("%.0f")}")
}

// 2. 各州销售分析
println("\n2. 各州销售分析...")
val stateSales = coffeeData
  .map(data => (data.state, data.coffeeSales))
  .groupByKey()
  .map { case (state, sales) =>
    val salesList = sales.toList
    val total = salesList.sum
    val avg = total / salesList.length
    (state, total, avg, salesList.length)
  }
  .sortBy(_._2, ascending = false)

println("各州销售统计：")
println("州名\t\t总销售量\t平均销售量\t记录数")
stateSales.collect().foreach { case (state, total, avg, count) =>
  println(f"$state%-12s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count")
}

// 3. 市场分析
println("\n3. 市场分析...")
val marketSales = coffeeData
  .map(data => (data.market, data.coffeeSales))
  .groupByKey()
  .map { case (market, sales) =>
    val salesList = sales.toList
    val total = salesList.sum
    val avg = total / salesList.length
    (market, total, avg, salesList.length)
  }
  .sortBy(_._2, ascending = false)

println("各市场销售统计：")
marketSales.collect().foreach { case (market, total, avg, count) =>
  println(f"$market%-15s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count")
}

// 4. 整体统计
println("\n4. 整体统计分析...")
val allData = coffeeData.collect()
val avgSales = allData.map(_.coffeeSales).sum / allData.length
val avgProfit = allData.map(_.profit).sum / allData.length
val avgMargin = allData.map(_.margin).sum / allData.length
val avgCogs = allData.map(_.cogs).sum / allData.length
val avgMarketing = allData.map(_.marketing).sum / allData.length

println(f"平均销售额：${avgSales.formatted("%.2f")}")
println(f"平均利润：${avgProfit.formatted("%.2f")}")
println(f"平均利润率：${avgMargin.formatted("%.2f")}")
println(f"平均成本：${avgCogs.formatted("%.2f")}")
println(f"平均营销费用：${avgMarketing.formatted("%.2f")}")

// 5. 咖啡类型分析
println("\n5. 咖啡类型分析...")
val typeAnalysis = coffeeData
  .map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.margin)))
  .groupByKey()
  .map { case (coffeeType, values) =>
    val valuesList = values.toList
    val avgSales = valuesList.map(_._1).sum / valuesList.length
    val avgProfit = valuesList.map(_._2).sum / valuesList.length
    val avgMargin = valuesList.map(_._3).sum / valuesList.length
    (coffeeType, avgSales, avgProfit, avgMargin, valuesList.length)
  }
  .sortBy(_._2, ascending = false)

println("按咖啡类型分析：")
println("类型\t\t平均销售量\t平均利润\t平均利润率\t记录数")
val typeResults = typeAnalysis.collect()
typeResults.foreach { case (coffeeType, sales, profit, margin, count) =>
  println(f"$coffeeType%-10s\t${sales.formatted("%.2f")}%-10s\t${profit.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}%-10s\t$count")
}

// 保存咖啡分析结果
val productResults = productSales.take(10).zipWithIndex.map { case ((product, sales), index) =>
  f"${index + 1}. $product: ${sales.formatted("%.0f")}"
}.mkString("\n")

val stateResults = stateSales.collect().map { case (state, total, avg, count) =>
  f"$state%-12s\t${total.formatted("%.0f")}%-10s\t${avg.formatted("%.2f")}%-10s\t$count"
}.mkString("\n")

val typeResultsText = typeResults.map { case (coffeeType, sales, profit, margin, count) =>
  f"$coffeeType%-10s\t${sales.formatted("%.2f")}%-10s\t${profit.formatted("%.2f")}%-10s\t${margin.formatted("%.2f")}%-10s\t$count"
}.mkString("\n")

val coffeeResults = sc.parallelize(Seq(
  "=== 咖啡连锁店数据分析结果 ===",
  s"总记录数：$validCoffeeRecords",
  "",
  "产品销售量排名（Top 10）：",
  productResults,
  "",
  "各州销售统计：",
  "州名\t\t总销售量\t平均销售量\t记录数",
  stateResults,
  "",
  "整体统计：",
  f"平均销售额：${avgSales.formatted("%.2f")}",
  f"平均利润：${avgProfit.formatted("%.2f")}",
  f"平均利润率：${avgMargin.formatted("%.2f")}",
  f"平均成本：${avgCogs.formatted("%.2f")}",
  f"平均营销费用：${avgMarketing.formatted("%.2f")}",
  "",
  "按咖啡类型分析：",
  "类型\t\t平均销售量\t平均利润\t平均利润率\t记录数",
  typeResultsText
))

coffeeResults.coalesce(1).saveAsTextFile("/home/<USER>/me/coffee_analysis_results")
println("\n咖啡分析结果已保存到：/home/<USER>/me/coffee_analysis_results/")

println("\n=== 第二部分完成 ===")
println("\n=== 所有分析完成 ===")

// 显示结果文件
println("\n生成的结果文件：")
println("- /home/<USER>/me/people_age_analysis/part-00000      # 人口年龄分析结果")
println("- /home/<USER>/me/coffee_analysis_results/part-00000  # 咖啡分析结果")
println("\n可以使用以下命令查看结果：")
println("scala> sc.textFile(\"/home/<USER>/me/people_age_analysis/part-00000\").collect().foreach(println)")
println("scala> sc.textFile(\"/home/<USER>/me/coffee_analysis_results/part-00000\").collect().foreach(println)")
