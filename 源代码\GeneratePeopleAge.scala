import scala.util.Random
import java.io.PrintWriter
import java.io.File

/**
 * 生成模拟人口年龄数据文件
 * 数据格式：序号 年龄
 * 年龄范围：18-90岁
 */
object GeneratePeopleAge {
  def main(args: Array[String]): Unit = {
    // 设置参数
    val numRecords = 1000  // 生成1000条记录
    val minAge = 18        // 最小年龄
    val maxAge = 90        // 最大年龄
    val outputFile = "/home/<USER>/me/peopleage.txt"
    
    println(s"开始生成 $numRecords 条人口年龄数据...")
    
    // 创建随机数生成器
    val random = new Random()
    
    // 创建输出文件
    val writer = new PrintWriter(new File(outputFile))
    
    try {
      // 生成数据并写入文件
      for (i <- 1 to numRecords) {
        val age = minAge + random.nextInt(maxAge - minAge + 1)
        writer.println(s"$i\t$age")
        
        // 每100条记录显示进度
        if (i % 100 == 0) {
          println(s"已生成 $i 条记录...")
        }
      }
      
      println(s"数据生成完成！文件保存为：$outputFile")
      println("数据示例（前10行）：")
      
    } finally {
      writer.close()
    }
    
    // 显示生成的数据示例
    val source = scala.io.Source.fromFile(outputFile)
    try {
      val lines = source.getLines().take(10).toList
      lines.foreach(println)
    } finally {
      source.close()
    }
    
    println(s"\n总共生成了 $numRecords 条记录")
    println("数据格式：序号\\t年龄")
  }
}
